2025-07-16 16:10:30,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9)
2025-07-16 16:13:04,583 WARNING database DDL Query made to DB:
alter table `tabVelocetec Costing Detail` add column if not exists parent varchar(140)
2025-07-16 16:13:04,584 WARNING database DDL Query made to DB:
alter table `tabVelocetec Costing Detail` add column if not exists parenttype varchar(140)
2025-07-16 16:13:04,585 WARNING database DDL Query made to DB:
alter table `tabVelocetec Costing Detail` add column if not exists parentfield varchar(140)
2025-07-16 16:13:04,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing Detail` MODIFY `batch_cost` decimal(21,9) not null default 0, MODIFY `sell_cost_each` decimal(21,9) not null default 0, MODIFY `sub_con` decimal(21,9) not null default 0, MODIFY `edm` decimal(21,9) not null default 0, MODIFY `sell_price_each` decimal(21,9) not null default 0, MODIFY `delivery_cost` decimal(21,9) not null default 0, MODIFY `assembly` decimal(21,9) not null default 0, MODIFY `tool_making` decimal(21,9) not null default 0, MODIFY `fixings` decimal(21,9) not null default 0, MODIFY `no_of_shipments` decimal(21,9) not null default 0, MODIFY `line_total` decimal(21,9) not null default 0, MODIFY `other` decimal(21,9) not null default 0, MODIFY `material_price` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `delivery_each` decimal(21,9) not null default 0, MODIFY `each_cost` decimal(21,9) not null default 0, MODIFY `own_sell_cost_each` decimal(21,9) not null default 0, MODIFY `mass_finishing` decimal(21,9) not null default 0, MODIFY `design` decimal(21,9) not null default 0, MODIFY `finishing` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9), MODIFY `inspection` decimal(21,9) not null default 0, MODIFY `turning` decimal(21,9) not null default 0
2025-07-24 14:06:17,386 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-24 14:06:18,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-24 14:06:20,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-24 14:06:21,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing Detail` MODIFY `sell_price_each` decimal(21,9) not null default 0, MODIFY `each_cost` decimal(21,9) not null default 0, MODIFY `design` decimal(21,9) not null default 0, MODIFY `material_price` decimal(21,9) not null default 0, MODIFY `finishing` decimal(21,9) not null default 0, MODIFY `other` decimal(21,9) not null default 0, MODIFY `turning` decimal(21,9) not null default 0, MODIFY `tool_making` decimal(21,9) not null default 0, MODIFY `fixings` decimal(21,9) not null default 0, MODIFY `no_of_shipments` decimal(21,9) not null default 0, MODIFY `batch_cost` decimal(21,9) not null default 0, MODIFY `inspection` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9), MODIFY `sub_con` decimal(21,9) not null default 0, MODIFY `line_total` decimal(21,9) not null default 0, MODIFY `assembly` decimal(21,9) not null default 0, MODIFY `sell_cost_each` decimal(21,9) not null default 0, MODIFY `edm` decimal(21,9) not null default 0, MODIFY `delivery_each` decimal(21,9) not null default 0, MODIFY `own_sell_cost_each` decimal(21,9) not null default 0, MODIFY `mass_finishing` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `delivery_cost` decimal(21,9) not null default 0
2025-07-24 14:26:33,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing` ADD COLUMN `internal_notes` text, ADD COLUMN `external_notes` text
2025-07-24 14:26:33,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing` MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `delivery_cost` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-24 14:32:28,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing` MODIFY `delivery_cost` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-24 14:33:03,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing` MODIFY `delivery_cost` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-31 11:56:11,396 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-31 11:56:13,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-31 11:56:15,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-31 11:56:17,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Material Cut Item` MODIFY `basic_rate` decimal(21,9) not null default 0
2025-07-31 11:56:17,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing` MODIFY `delivery_cost` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-31 11:56:18,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing Detail` ADD COLUMN `add_to_parts_sell_cost_each` decimal(21,9) not null default 0
2025-07-31 11:56:18,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabVelocetec Costing Detail` MODIFY `sub_con` decimal(21,9) not null default 0, MODIFY `tool_making` decimal(21,9) not null default 0, MODIFY `fixings` decimal(21,9) not null default 0, MODIFY `line_total` decimal(21,9) not null default 0, MODIFY `sell_cost_each` decimal(21,9) not null default 0, MODIFY `turning` decimal(21,9) not null default 0, MODIFY `delivery_total` decimal(21,9) not null default 0, MODIFY `inspection` decimal(21,9) not null default 0, MODIFY `other` decimal(21,9) not null default 0, MODIFY `finishing` decimal(21,9) not null default 0, MODIFY `no_of_shipments` decimal(21,9) not null default 0, MODIFY `sell_price_each` decimal(21,9) not null default 0, MODIFY `mass_finishing` decimal(21,9) not null default 0, MODIFY `design` decimal(21,9) not null default 0, MODIFY `assembly` decimal(21,9) not null default 0, MODIFY `each_cost` decimal(21,9) not null default 0, MODIFY `delivery_each` decimal(21,9) not null default 0, MODIFY `material_price` decimal(21,9) not null default 0, MODIFY `batch_cost` decimal(21,9) not null default 0, MODIFY `edm` decimal(21,9) not null default 0, MODIFY `own_sell_cost_each` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9), MODIFY `delivery_cost` decimal(21,9) not null default 0
2025-07-31 12:49:18,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0
2025-07-31 12:49:19,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` ADD COLUMN `custom_internal_notes` text
2025-07-31 12:49:19,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-31 12:49:19,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-31 12:49:19,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` ADD COLUMN `custom_external_notes` text
2025-07-31 12:49:19,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0
2025-07-31 12:49:19,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0
2025-07-31 12:50:36,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) not null default 0
2025-07-31 15:25:56,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-07-31 15:25:57,100 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `custom_internal_notes` text
2025-07-31 15:25:57,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0
2025-07-31 15:25:57,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-31 15:25:57,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-07-31 15:25:58,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-07-31 15:32:33,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0
2025-07-31 15:32:33,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `custom_internal_notes` text
2025-07-31 15:32:33,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0
2025-07-31 15:32:34,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0
2025-07-31 15:32:34,448 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `custom_external_notes` text
2025-07-31 15:32:34,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-07-31 15:32:34,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0
2025-07-31 16:43:54,116 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-31 16:43:55,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-31 16:43:58,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-01 10:42:32,918 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0
2025-08-01 11:01:32,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `disassembled_qty` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0
